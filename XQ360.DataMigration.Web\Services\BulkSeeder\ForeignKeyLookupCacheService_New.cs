using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Collections.Concurrent;
using System.Diagnostics;
using XQ360.DataMigration.Web.Models;
using XQ360.DataMigration.Services;

namespace XQ360.DataMigration.Web.Services.BulkSeeder;

/// <summary>
/// High-performance foreign key lookup cache service using only production tables
/// Provides optimized lookups for Customer, Site, Department, Model, and Module entities
/// Uses session-scoped in-memory caching with automatic cleanup - NO STAGING TABLES
/// </summary>
public class ForeignKeyLookupCacheService : IForeignKeyLookupCacheService
{
    private readonly ILogger<ForeignKeyLookupCacheService> _logger;
    private readonly BulkSeederConfiguration _options;
    private readonly IServiceScopeFactory _scopeFactory;

    // High-performance concurrent caches with LRU eviction
    private readonly ConcurrentDictionary<string, CacheEntry<Guid>> _customerCache = new();
    private readonly ConcurrentDictionary<string, CacheEntry<Guid>> _siteCache = new();
    private readonly ConcurrentDictionary<string, CacheEntry<Guid>> _departmentCache = new();
    private readonly ConcurrentDictionary<string, CacheEntry<Guid>> _modelCache = new();
    private readonly ConcurrentDictionary<string, CacheEntry<ModuleLookupResult>> _moduleCache = new();

    // Performance counters
    private long _totalLookups = 0;
    private long _cacheHits = 0;
    private long _cacheMisses = 0;
    private long _evictedItems = 0;

    // Cache configuration
    private readonly int _maxCacheSize = 10000; // Maximum items per cache
    private readonly TimeSpan _defaultTtl = TimeSpan.FromHours(1);
    private readonly TimeSpan _sessionTtl = TimeSpan.FromHours(24);

    public ForeignKeyLookupCacheService(
        ILogger<ForeignKeyLookupCacheService> logger,
        IOptions<BulkSeederConfiguration> options,
        IServiceScopeFactory scopeFactory)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
        _scopeFactory = scopeFactory ?? throw new ArgumentNullException(nameof(scopeFactory));
    }

    private string GetConnectionString()
    {
        using var scope = _scopeFactory.CreateScope();
        var environmentService = scope.ServiceProvider.GetRequiredService<IEnvironmentConfigurationService>();
        return environmentService.CurrentMigrationConfiguration.DatabaseConnection;
    }

    public async Task InitializeCacheAsync(Guid sessionId, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        _logger.LogInformation("Initializing FK lookup cache for session {SessionId}", sessionId);

        try
        {
            // Clear any existing cache entries
            _customerCache.Clear();
            _siteCache.Clear();
            _departmentCache.Clear();
            _modelCache.Clear();
            _moduleCache.Clear();

            // Reset counters
            _totalLookups = 0;
            _cacheHits = 0;
            _cacheMisses = 0;
            _evictedItems = 0;

            stopwatch.Stop();
            _logger.LogInformation("FK lookup cache initialized in {Duration}ms for session {SessionId}",
                stopwatch.ElapsedMilliseconds, sessionId);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Failed to initialize FK lookup cache for session {SessionId} after {Duration}ms",
                sessionId, stopwatch.ElapsedMilliseconds);
            throw;
        }
    }

    public async Task<CacheResult<Guid>> GetCustomerIdAsync(
        string customerName,
        string? dealerName = null,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        Interlocked.Increment(ref _totalLookups);

        try
        {
            if (string.IsNullOrWhiteSpace(customerName))
                return CacheResult<Guid>.NotFound(stopwatch.Elapsed);

            var cacheKey = dealerName != null ? $"{customerName}|{dealerName}" : customerName;

            // Check in-memory cache first
            if (_customerCache.TryGetValue(cacheKey, out var cachedEntry) && !cachedEntry.IsExpired)
            {
                cachedEntry.UpdateAccessTime();
                Interlocked.Increment(ref _cacheHits);
                stopwatch.Stop();
                return CacheResult<Guid>.Success(cachedEntry.Value, true, stopwatch.Elapsed);
            }

            Interlocked.Increment(ref _cacheMisses);

            // Query production tables directly (no staging tables)
            using var connection = new SqlConnection(GetConnectionString());
            await connection.OpenAsync(cancellationToken);

            const string sql = @"
                SELECT c.[Id] 
                FROM [dbo].[Customer] c
                INNER JOIN [dbo].[Dealer] d ON c.[DealerId] = d.[Id]
                WHERE c.[CompanyName] = @CustomerName 
                AND (@DealerName IS NULL OR d.[Name] = @DealerName)";

            using var cmd = new SqlCommand(sql, connection);
            cmd.Parameters.AddWithValue("@CustomerName", customerName);
            cmd.Parameters.AddWithValue("@DealerName", (object?)dealerName ?? DBNull.Value);

            var result = await cmd.ExecuteScalarAsync(cancellationToken);
            if (result != null && result != DBNull.Value)
            {
                var customerId = (Guid)result;
                
                // Cache in memory for session duration
                _customerCache.TryAdd(cacheKey, new CacheEntry<Guid>(customerId, _sessionTtl));
                
                // Evict old entries if cache is getting too large
                if (_customerCache.Count > _maxCacheSize)
                {
                    EvictOldestEntries(_customerCache);
                }

                stopwatch.Stop();
                return CacheResult<Guid>.Success(customerId, false, stopwatch.Elapsed);
            }

            stopwatch.Stop();
            return CacheResult<Guid>.NotFound(stopwatch.Elapsed);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Error looking up customer {CustomerName} with dealer {DealerName}", 
                customerName, dealerName);
            return CacheResult<Guid>.Error(ex.Message, stopwatch.Elapsed);
        }
    }

    public async Task<CacheResult<Guid>> GetSiteIdAsync(
        string customerName,
        string siteName,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        Interlocked.Increment(ref _totalLookups);

        try
        {
            if (string.IsNullOrWhiteSpace(customerName) || string.IsNullOrWhiteSpace(siteName))
                return CacheResult<Guid>.NotFound(stopwatch.Elapsed);

            var cacheKey = $"{customerName}|{siteName}";

            // Check in-memory cache first
            if (_siteCache.TryGetValue(cacheKey, out var cachedEntry) && !cachedEntry.IsExpired)
            {
                cachedEntry.UpdateAccessTime();
                Interlocked.Increment(ref _cacheHits);
                stopwatch.Stop();
                return CacheResult<Guid>.Success(cachedEntry.Value, true, stopwatch.Elapsed);
            }

            Interlocked.Increment(ref _cacheMisses);

            // Query production tables directly
            using var connection = new SqlConnection(GetConnectionString());
            await connection.OpenAsync(cancellationToken);

            const string sql = @"
                SELECT s.[Id] 
                FROM [dbo].[Site] s
                INNER JOIN [dbo].[Customer] c ON s.[CustomerId] = c.[Id]
                WHERE c.[CompanyName] = @CustomerName 
                AND s.[Name] = @SiteName";

            using var cmd = new SqlCommand(sql, connection);
            cmd.Parameters.AddWithValue("@CustomerName", customerName);
            cmd.Parameters.AddWithValue("@SiteName", siteName);

            var result = await cmd.ExecuteScalarAsync(cancellationToken);
            if (result != null && result != DBNull.Value)
            {
                var siteId = (Guid)result;
                
                // Cache in memory for session duration
                _siteCache.TryAdd(cacheKey, new CacheEntry<Guid>(siteId, _sessionTtl));
                
                // Evict old entries if cache is getting too large
                if (_siteCache.Count > _maxCacheSize)
                {
                    EvictOldestEntries(_siteCache);
                }

                stopwatch.Stop();
                return CacheResult<Guid>.Success(siteId, false, stopwatch.Elapsed);
            }

            stopwatch.Stop();
            return CacheResult<Guid>.NotFound(stopwatch.Elapsed);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Error looking up site {SiteName} for customer {CustomerName}", 
                siteName, customerName);
            return CacheResult<Guid>.Error(ex.Message, stopwatch.Elapsed);
        }
    }

    public async Task<CacheResult<Guid>> GetDepartmentIdAsync(
        string customerName,
        string siteName,
        string departmentName,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        Interlocked.Increment(ref _totalLookups);

        try
        {
            if (string.IsNullOrWhiteSpace(customerName) || string.IsNullOrWhiteSpace(siteName) || string.IsNullOrWhiteSpace(departmentName))
                return CacheResult<Guid>.NotFound(stopwatch.Elapsed);

            var cacheKey = $"{customerName}|{siteName}|{departmentName}";

            // Check in-memory cache first
            if (_departmentCache.TryGetValue(cacheKey, out var cachedEntry) && !cachedEntry.IsExpired)
            {
                cachedEntry.UpdateAccessTime();
                Interlocked.Increment(ref _cacheHits);
                stopwatch.Stop();
                return CacheResult<Guid>.Success(cachedEntry.Value, true, stopwatch.Elapsed);
            }

            Interlocked.Increment(ref _cacheMisses);

            // Query production tables directly
            using var connection = new SqlConnection(GetConnectionString());
            await connection.OpenAsync(cancellationToken);

            const string sql = @"
                SELECT d.[Id] 
                FROM [dbo].[Department] d
                INNER JOIN [dbo].[Site] s ON d.[SiteId] = s.[Id]
                INNER JOIN [dbo].[Customer] c ON s.[CustomerId] = c.[Id]
                WHERE c.[CompanyName] = @CustomerName 
                AND s.[Name] = @SiteName
                AND d.[Name] = @DepartmentName";

            using var cmd = new SqlCommand(sql, connection);
            cmd.Parameters.AddWithValue("@CustomerName", customerName);
            cmd.Parameters.AddWithValue("@SiteName", siteName);
            cmd.Parameters.AddWithValue("@DepartmentName", departmentName);

            var result = await cmd.ExecuteScalarAsync(cancellationToken);
            if (result != null && result != DBNull.Value)
            {
                var departmentId = (Guid)result;
                
                // Cache in memory for session duration
                _departmentCache.TryAdd(cacheKey, new CacheEntry<Guid>(departmentId, _sessionTtl));
                
                // Evict old entries if cache is getting too large
                if (_departmentCache.Count > _maxCacheSize)
                {
                    EvictOldestEntries(_departmentCache);
                }

                stopwatch.Stop();
                return CacheResult<Guid>.Success(departmentId, false, stopwatch.Elapsed);
            }

            stopwatch.Stop();
            return CacheResult<Guid>.NotFound(stopwatch.Elapsed);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Error looking up department {DepartmentName} for site {SiteName} and customer {CustomerName}", 
                departmentName, siteName, customerName);
            return CacheResult<Guid>.Error(ex.Message, stopwatch.Elapsed);
        }
    }
